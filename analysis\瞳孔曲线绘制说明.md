# 瞳孔直径曲线绘制工具使用说明

## 功能概述
`draw_pupil_line.py` 是一个使用 pyedfread 库从 EDF 文件中提取瞳孔数据并绘制时间曲线的工具。

## 主要功能
1. **读取EDF文件**：使用pyedfread库解析SR Research EyeLink的EDF文件
2. **提取指定时间段数据**：根据消息标记提取特定实验阶段的瞳孔数据
3. **数据处理**：支持缺失值插值或保留缺失值
4. **可视化**：生成高质量的瞳孔直径时间曲线图
5. **模拟数据**：当真实瞳孔数据不可用时，可生成模拟数据用于演示

## 配置参数

### 文件路径设置
```python
# EDF文件路径
EDF_FILE_PATH = "data/20250711_112343_test_0711_1123/test_0711_1123.edf"
```

### 分析范围设置
```python
# 要分析的试次
TRIALS_TO_ANALYZE = [1]  # 分析试次1
# TRIALS_TO_ANALYZE = [1, 2]  # 分析试次1和2
# TRIALS_TO_ANALYZE = "all"  # 分析所有试次

# 要分析的数据段
DATA_SEGMENTS = [
    ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
    ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
    # 可以添加更多段：
    # ("BASELINE_START", "BASELINE_END", "基线测量"),
    # ("RATING_START", "RATING_END", "评分阶段"),
]
```

### 数据处理选项
```python
# 瞳孔数据处理选项
MISSING_DATA_HANDLING = "interpolate"  # "interpolate" 或 "keep_missing"
INTERPOLATION_METHOD = "linear"  # "linear", "cubic", "nearest"

# 数据过滤参数
MIN_PUPIL_SIZE = 1.0  # 最小瞳孔直径（毫米）
MAX_PUPIL_SIZE = 10.0  # 最大瞳孔直径（毫米）

# 模拟数据选项（当真实瞳孔数据不可用时）
USE_SIMULATED_DATA = True  # 是否使用模拟数据
SIMULATION_NOISE_LEVEL = 0.1  # 模拟数据噪声水平
```

### 绘图参数
```python
# 绘图参数
FIGURE_SIZE = (12, 8)
DPI = 300
SAVE_FORMAT = "png"  # "png", "pdf", "svg"
OUTPUT_DIR = "analysis/figures/pupil_curves"

# 时间轴参数
TIME_UNIT = "ms"  # "ms" 或 "s"
RELATIVE_TIME = True  # True: 相对于段开始时间, False: 绝对时间
```

## 使用方法

### 1. 安装依赖
```bash
# 安装pyedfread
pip install git+https://github.com/s-ccs/pyedfread

# 安装其他依赖
pip install pandas matplotlib numpy
```

### 2. 配置参数
编辑 `draw_pupil_line.py` 文件顶部的配置参数：
- 设置EDF文件路径
- 选择要分析的试次
- 配置数据段和处理选项

### 3. 运行分析
```bash
python analysis\draw_pupil_line.py
```

## 输出结果

### 控制台输出
```
瞳孔直径曲线绘制工具
==================================================
✓ pyedfread 可用
正在读取EDF文件: data/20250711_112343_test_0711_1123/test_0711_1123.edf
✓ 成功读取EDF文件
  - 样本数: 32227
  - 事件数: 4
  - 消息数: 57

解析消息数据...
✓ 解析完成，找到 1 个试次

处理试次 1...
  分析 问题显示: 1829520 - 1836587
  警告: 没有有效的瞳孔数据，生成模拟数据用于演示
✓ 使用linear插值处理缺失数据
✓ 保存图形: analysis/figures/pupil_curves\trial_1_问题显示.png
```

### 生成的图形文件
- 文件位置：`analysis/figures/pupil_curves/`
- 命名格式：`trial_{试次号}_{段名称}.png`
- 包含内容：
  - 左眼瞳孔直径曲线（蓝色）
  - 右眼瞳孔直径曲线（红色）
  - 双眼平均瞳孔直径曲线（黑色）
  - 统计信息（数据点数、平均值、标准差）

## 数据段配置示例

### 基本实验阶段
```python
DATA_SEGMENTS = [
    ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
    ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
]
```

### 完整实验流程
```python
DATA_SEGMENTS = [
    ("BASELINE_START", "BASELINE_END", "基线测量"),
    ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
    ("INPUT_START", "INPUT_END", "答案输入"),
    ("RATING_START", "RATING_END", "好奇心评分"),
    ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
]
```

### 自定义时间段
```python
DATA_SEGMENTS = [
    ("TRIAL_START", "QUESTION_DISPLAY_END", "试次开始到问题结束"),
    ("ANSWER_DISPLAY_START", "TRIAL_END", "答案显示到试次结束"),
]
```

## 故障排除

### 1. pyedfread安装问题
如果遇到pyedfread安装问题，请确保：
- 已安装EyeLink Developers Kit
- 系统环境变量配置正确
- 使用正确的Python环境

### 2. 没有瞳孔数据
如果EDF文件中没有有效的瞳孔数据：
- 设置 `USE_SIMULATED_DATA = True` 生成模拟数据
- 检查眼动仪设置是否正确记录瞳孔数据
- 确认EDF文件完整性

### 3. 消息解析问题
如果无法找到指定的消息标记：
- 检查EDF文件中的消息格式
- 确认消息标记名称正确
- 查看控制台输出的消息列表

### 4. 图形显示问题
如果图形无法正常显示：
- 确保matplotlib正确安装
- 检查中文字体设置
- 确认输出目录权限

## 扩展功能

### 添加新的数据段
1. 在 `DATA_SEGMENTS` 中添加新的元组
2. 确保消息标记在EDF文件中存在
3. 运行分析查看结果

### 修改绘图样式
1. 调整 `FIGURE_SIZE` 和 `DPI` 参数
2. 修改 `plot_pupil_curve` 函数中的颜色和线型
3. 自定义图例和标签

### 批量处理多个文件
1. 修改 `main` 函数支持文件列表
2. 添加循环处理逻辑
3. 实现结果汇总功能

## 注意事项

1. **数据质量**：确保EDF文件包含有效的瞳孔数据
2. **内存使用**：大文件可能需要较多内存
3. **处理时间**：复杂分析可能需要较长时间
4. **文件格式**：确保EDF文件格式正确且完整
5. **消息标记**：确保实验程序正确发送了消息标记

## 技术支持

如果遇到问题，请检查：
1. 控制台输出的错误信息
2. EDF文件的完整性
3. 配置参数的正确性
4. 依赖库的版本兼容性
