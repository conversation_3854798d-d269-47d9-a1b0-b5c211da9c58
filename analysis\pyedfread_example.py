#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
pyedfread使用示例 - 展示如何使用pyedfread库分析EyeLink数据
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from typing import Dict, List, Tuple

# 设置matplotlib中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def check_pyedfread():
    """检查pyedfread是否可用"""
    try:
        import pyedfread
        print("✓ pyedfread 可用")
        return True
    except ImportError:
        print("❌ pyedfread 未安装")
        print("请安装: pip install git+https://github.com/s-ccs/pyedfread")
        return False

def load_edf_with_pyedfread(edf_path: str) -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    使用pyedfread加载EDF文件
    
    Returns:
        samples: 样本数据 (包含瞳孔大小、注视点等)
        events: 事件数据 (包含注视、眼跳等)
        messages: 消息数据 (包含实验标记)
    """
    try:
        import pyedfread
        print(f"正在读取EDF文件: {edf_path}")
        
        # 读取EDF文件 - 这是pyedfread的核心功能
        samples, events, messages = pyedfread.read_edf(edf_path)
        
        print(f"✓ 成功读取EDF文件")
        print(f"  - 样本数: {len(samples)}")
        print(f"  - 事件数: {len(events)}")
        print(f"  - 消息数: {len(messages)}")
        
        return samples, events, messages
        
    except Exception as e:
        print(f"❌ 读取EDF文件失败: {e}")
        return None, None, None

def analyze_pupil_data(samples: pd.DataFrame, messages: pd.DataFrame):
    """分析瞳孔数据"""
    print("\n分析瞳孔数据...")
    
    # 检查样本数据的列
    print(f"样本数据列: {list(samples.columns)}")
    
    # 检查瞳孔数据列
    pupil_columns = [col for col in samples.columns if 'pa_' in col]
    print(f"瞳孔数据列: {pupil_columns}")
    
    if not pupil_columns:
        print("❌ 未找到瞳孔数据列")
        return
    
    # 分析左眼瞳孔数据
    if 'pa_left' in samples.columns:
        left_pupil = samples['pa_left']
        valid_left = left_pupil[left_pupil > 0]
        print(f"左眼瞳孔数据:")
        print(f"  - 总数据点: {len(left_pupil)}")
        print(f"  - 有效数据点: {len(valid_left)}")
        print(f"  - 有效率: {len(valid_left)/len(left_pupil)*100:.1f}%")
        if len(valid_left) > 0:
            print(f"  - 范围: {valid_left.min():.1f} - {valid_left.max():.1f}")
            print(f"  - 平均: {valid_left.mean():.1f} ± {valid_left.std():.1f}")
    
    # 分析右眼瞳孔数据
    if 'pa_right' in samples.columns:
        right_pupil = samples['pa_right']
        valid_right = right_pupil[right_pupil > 0]
        print(f"右眼瞳孔数据:")
        print(f"  - 总数据点: {len(right_pupil)}")
        print(f"  - 有效数据点: {len(valid_right)}")
        print(f"  - 有效率: {len(valid_right)/len(right_pupil)*100:.1f}%")
        if len(valid_right) > 0:
            print(f"  - 范围: {valid_right.min():.1f} - {valid_right.max():.1f}")
            print(f"  - 平均: {valid_right.mean():.1f} ± {valid_right.std():.1f}")

def extract_trial_segments(samples: pd.DataFrame, messages: pd.DataFrame):
    """提取试次数据段"""
    print("\n提取试次数据段...")
    
    # 检查消息数据
    print(f"消息数据列: {list(messages.columns)}")
    print(f"前5条消息:")
    for i, (_, row) in enumerate(messages.head().iterrows()):
        print(f"  {i+1}. {row['time']}: {row['message']}")
    
    # 查找试次相关的消息
    trial_messages = messages[messages['message'].str.contains('TRIAL|QUESTION|ANSWER', case=False, na=False)]
    print(f"\n找到 {len(trial_messages)} 条试次相关消息")
    
    # 解析试次事件
    trial_events = {}
    for _, row in trial_messages.iterrows():
        timestamp = row['time']
        message = row['message']
        
        if 'QUESTION_DISPLAY_START' in message:
            trial_num = 1  # 简化处理，实际应该从消息中解析试次号
            if trial_num not in trial_events:
                trial_events[trial_num] = {}
            trial_events[trial_num]['question_start'] = timestamp
            
        elif 'QUESTION_DISPLAY_END' in message:
            trial_num = 1
            if trial_num not in trial_events:
                trial_events[trial_num] = {}
            trial_events[trial_num]['question_end'] = timestamp
    
    print(f"解析到 {len(trial_events)} 个试次的事件")
    
    # 提取第一个试次的瞳孔数据
    if trial_events:
        trial_num = list(trial_events.keys())[0]
        trial_data = trial_events[trial_num]
        
        if 'question_start' in trial_data and 'question_end' in trial_data:
            start_time = trial_data['question_start']
            end_time = trial_data['question_end']
            
            print(f"\n提取试次 {trial_num} 的瞳孔数据:")
            print(f"  时间范围: {start_time} - {end_time} ({end_time - start_time} ms)")
            
            # 提取时间段内的样本数据
            trial_samples = samples[
                (samples['time'] >= start_time) & 
                (samples['time'] <= end_time)
            ].copy()
            
            print(f"  提取到 {len(trial_samples)} 个数据点")
            
            # 计算双眼平均瞳孔大小
            if 'pa_left' in trial_samples.columns and 'pa_right' in trial_samples.columns:
                trial_samples['pupil_avg'] = trial_samples[['pa_left', 'pa_right']].mean(axis=1)
                
                # 绘制瞳孔变化曲线
                plot_pupil_curve(trial_samples, trial_num, start_time)
            
            return trial_samples
    
    return None

def plot_pupil_curve(trial_data: pd.DataFrame, trial_num: int, start_time: int):
    """绘制瞳孔变化曲线"""
    print(f"\n绘制试次 {trial_num} 的瞳孔变化曲线...")
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 计算相对时间（毫秒）
    relative_time = trial_data['time'] - start_time
    
    # 绘制左眼、右眼和平均瞳孔大小
    if 'pa_left' in trial_data.columns:
        valid_left = trial_data['pa_left'] > 0
        ax.plot(relative_time[valid_left], trial_data.loc[valid_left, 'pa_left'], 
                'b-', alpha=0.7, label='左眼', linewidth=1)
    
    if 'pa_right' in trial_data.columns:
        valid_right = trial_data['pa_right'] > 0
        ax.plot(relative_time[valid_right], trial_data.loc[valid_right, 'pa_right'], 
                'r-', alpha=0.7, label='右眼', linewidth=1)
    
    if 'pupil_avg' in trial_data.columns:
        valid_avg = trial_data['pupil_avg'] > 0
        ax.plot(relative_time[valid_avg], trial_data.loc[valid_avg, 'pupil_avg'], 
                'k-', label='双眼平均', linewidth=2)
    
    # 设置图形属性
    ax.set_xlabel('时间 (毫秒)', fontsize=12)
    ax.set_ylabel('瞳孔直径 (像素)', fontsize=12)
    ax.set_title(f'试次 {trial_num} - 瞳孔直径变化', fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 保存图形
    output_dir = "figures"
    os.makedirs(output_dir, exist_ok=True)
    filename = f"pupil_curve_trial_{trial_num}_pyedfread.png"
    filepath = os.path.join(output_dir, filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    print(f"✓ 保存图形: {filepath}")
    
    plt.show()

def main():
    """主函数 - pyedfread使用示例"""
    print("pyedfread使用示例")
    print("=" * 50)
    
    # 检查pyedfread
    if not check_pyedfread():
        return
    
    # 查找EDF文件
    data_dir = "../data"
    edf_files = []
    
    if os.path.exists(data_dir):
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith('.edf'):
                    edf_files.append(os.path.join(root, file))
    
    if not edf_files:
        print("❌ 未找到EDF文件")
        print("请确保data目录中有EDF文件")
        return
    
    print(f"找到 {len(edf_files)} 个EDF文件:")
    for edf_file in edf_files:
        print(f"  - {edf_file}")
    
    # 分析第一个EDF文件
    test_file = edf_files[0]
    print(f"\n分析文件: {test_file}")
    
    # 使用pyedfread读取数据
    samples, events, messages = load_edf_with_pyedfread(test_file)
    
    if samples is None:
        return
    
    # 分析瞳孔数据
    analyze_pupil_data(samples, messages)
    
    # 提取试次数据段
    trial_data = extract_trial_segments(samples, messages)
    
    print("\n" + "=" * 50)
    print("✓ pyedfread示例完成！")
    print("\n主要步骤:")
    print("1. 使用 pyedfread.read_edf() 读取EDF文件")
    print("2. 获得三个DataFrame: samples, events, messages")
    print("3. 从samples中提取瞳孔数据 (pa_left, pa_right)")
    print("4. 从messages中解析实验事件标记")
    print("5. 根据时间段提取特定试次的瞳孔数据")
    print("6. 进行数据清理、插值和可视化")

if __name__ == "__main__":
    main()
