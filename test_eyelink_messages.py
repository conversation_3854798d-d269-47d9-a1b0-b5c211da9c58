#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试EyeLink消息发送，确保没有中文字符
"""

import re
from eyelink_manager import EyeLinkManager

def test_eyelink_messages():
    """测试EyeLink消息发送，检查是否包含中文字符"""
    print("测试EyeLink消息发送...")
    print("=" * 50)
    
    # 创建EyeLink管理器（虚拟模式）
    eyelink = EyeLinkManager("test_participant", "test_data", (1920, 1080))
    
    # 存储所有发送的消息
    sent_messages = []
    
    # 重写send_message方法来捕获消息
    original_send_message = eyelink.send_message
    def capture_send_message(message):
        sent_messages.append(message)
        print(f"捕获消息: {message}")
        return original_send_message(message)
    
    eyelink.send_message = capture_send_message
    
    # 模拟实验流程
    print("\n1. 实验开始...")
    eyelink.log_experiment_start()
    
    print("\n2. 试次开始...")
    eyelink.log_trial_start(1, "世界上人口密度最高的国家是哪个?", question_id=55)
    
    print("\n3. 基线测量...")
    eyelink.log_baseline_start("FIXATION")
    eyelink.log_baseline_end("FIXATION")
    
    print("\n4. 题目显示...")
    eyelink.log_question_display("世界上人口密度最高的国家是哪个?", question_id=55)
    eyelink.log_question_end()
    
    print("\n5. 答案输入...")
    eyelink.log_input_start()
    eyelink.log_input_end("摩纳哥")
    
    print("\n6. 好奇心评分...")
    eyelink.log_rating_start("CURIOSITY")
    eyelink.log_rating_end("CURIOSITY", 4)
    
    print("\n7. 瞳孔基线...")
    eyelink.log_baseline_start("PUPIL")
    eyelink.log_baseline_end("PUPIL")
    
    print("\n8. 答案显示...")
    eyelink.log_answer_display("摩纳哥 - 摩纳哥是世界上人口密度最高的国家...", question_id=55)
    eyelink.log_answer_end()
    
    print("\n9. 愉悦度评分...")
    eyelink.log_rating_start("PLEASURE")
    eyelink.log_rating_end("PLEASURE", 3)
    
    print("\n10. 惊讶度评分...")
    eyelink.log_rating_start("SURPRISE")
    eyelink.log_rating_end("SURPRISE", 5)
    
    print("\n11. 试次结束...")
    eyelink.log_trial_end(1)
    
    print("\n12. 实验结束...")
    eyelink.log_experiment_end()
    
    # 检查所有消息是否包含中文字符
    print("\n" + "=" * 50)
    print("消息检查结果:")
    print("=" * 50)
    
    chinese_pattern = re.compile(r'[\u4e00-\u9fa5]')
    has_chinese = False
    
    for i, message in enumerate(sent_messages, 1):
        if chinese_pattern.search(message):
            print(f"✗ 消息 {i} 包含中文字符: {message}")
            has_chinese = True
        else:
            print(f"✓ 消息 {i} 无中文字符: {message}")
    
    print("\n" + "=" * 50)
    if has_chinese:
        print("❌ 检查失败：发现包含中文字符的消息")
        return False
    else:
        print("✅ 检查通过：所有消息均为英文")
        return True

def test_message_format():
    """测试消息格式是否符合要求"""
    print("\n测试消息格式...")
    print("=" * 50)
    
    # 创建EyeLink管理器（虚拟模式）
    eyelink = EyeLinkManager("test_participant", "test_data", (1920, 1080))
    
    # 存储所有发送的消息
    sent_messages = []
    
    # 重写send_message方法来捕获消息
    original_send_message = eyelink.send_message
    def capture_send_message(message):
        sent_messages.append(message)
        return original_send_message(message)
    
    eyelink.send_message = capture_send_message
    
    # 测试题目和答案消息格式
    eyelink.log_trial_start(1, "测试题目", question_id=123)
    eyelink.log_question_display("测试题目", question_id=123)
    eyelink.log_answer_display("测试答案", question_id=123)
    
    # 检查格式
    expected_patterns = [
        r'TRIAL_START \d+',
        r'QUESTION_ID question_\d+',
        r'QUESTION_DISPLAY_START',
        r'QUESTION_CONTENT question_\d+',
        r'ANSWER_DISPLAY_START',
        r'ANSWER_CONTENT answer_\d+'
    ]
    
    print("检查消息格式:")
    for pattern in expected_patterns:
        found = False
        for message in sent_messages:
            if re.search(pattern, message):
                print(f"✓ 找到格式: {pattern}")
                found = True
                break
        if not found:
            print(f"✗ 未找到格式: {pattern}")
    
    return True

if __name__ == "__main__":
    print("EyeLink消息测试")
    print("=" * 60)
    
    # 测试消息中文字符
    success1 = test_eyelink_messages()
    
    # 测试消息格式
    success2 = test_message_format()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！EyeLink消息已正确配置为英文格式")
    else:
        print("❌ 测试失败，需要进一步修改")
