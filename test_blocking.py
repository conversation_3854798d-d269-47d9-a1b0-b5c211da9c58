#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试题目屏蔽功能
"""

from experiment_materials import ExperimentMaterials
from experiment_config import BLOCKED_QUESTION_IDS

def test_blocking_feature():
    """测试屏蔽功能"""
    print("测试题目屏蔽功能")
    print("=" * 50)
    
    # 创建材料管理器
    materials = ExperimentMaterials()
    
    print(f"总题目数: {len(materials.question_answer_pairs)}")
    print(f"屏蔽题目数: {len(BLOCKED_QUESTION_IDS)}")
    print(f"屏蔽的题目ID: {BLOCKED_QUESTION_IDS}")
    
    # 测试随机选择（排除屏蔽题目）
    print("\n测试随机选择（排除屏蔽题目）:")
    selected = materials.get_random_questions(5, exclude_blocked=True)
    
    print("选中的题目:")
    for i, q in enumerate(selected, 1):
        print(f"{i}. ID {q['id']}: {q['question']}")
    
    # 验证选中的题目是否包含屏蔽题目
    blocked_in_selection = [q['id'] for q in selected if q['id'] in BLOCKED_QUESTION_IDS]
    if blocked_in_selection:
        print(f"\n❌ 错误：选中的题目包含屏蔽题目: {blocked_in_selection}")
    else:
        print(f"\n✅ 正确：选中的题目不包含任何屏蔽题目")
    
    # 测试随机选择（包含屏蔽题目）
    print("\n测试随机选择（包含屏蔽题目）:")
    selected_all = materials.get_random_questions(5, exclude_blocked=False)
    
    print("选中的题目:")
    for i, q in enumerate(selected_all, 1):
        blocked_status = " (屏蔽)" if q['id'] in BLOCKED_QUESTION_IDS else ""
        print(f"{i}. ID {q['id']}: {q['question']}{blocked_status}")

if __name__ == "__main__":
    test_blocking_feature()
