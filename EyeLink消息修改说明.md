# EyeLink消息修改说明

## 修改目标
确保所有发送给EyeLink的消息都是英文，没有中文字符。对于题目和答案内容，使用题库中的序号（question x, answer x）替代实际的中文内容。

## 修改内容

### 1. eyelink_manager.py 修改

#### 1.1 log_trial_start 方法
**修改前：**
```python
def log_trial_start(self, trial_num: int, question: str):
    self.send_message(f"TRIAL_START {trial_num}")
    question_short = question[:500].replace('\n', ' ').replace('\r', ' ')
    self.send_message(f"QUESTION_TEXT {question_short}")
```

**修改后：**
```python
def log_trial_start(self, trial_num: int, question: str, question_id: int = None):
    self.send_message(f"TRIAL_START {trial_num}")
    if question_id is not None:
        self.send_message(f"QUESTION_ID question_{question_id}")
    else:
        self.send_message(f"QUESTION_ID question_{trial_num}")
    print(f"题目内容: {question[:50]}...")  # 在控制台显示中文内容供调试
```

#### 1.2 log_question_display 方法
**修改前：**
```python
def log_question_display(self, question: str):
    self.send_message("QUESTION_DISPLAY_START")
    question_clean = question.replace('\n', ' ').replace('\r', ' ')
    self.send_message(f"QUESTION_CONTENT {question_clean}")
```

**修改后：**
```python
def log_question_display(self, question: str, question_id: int = None):
    self.send_message("QUESTION_DISPLAY_START")
    if question_id is not None:
        self.send_message(f"QUESTION_CONTENT question_{question_id}")
    else:
        self.send_message(f"QUESTION_CONTENT question_{self.current_trial}")
    print(f"显示题目: {question[:50]}...")  # 在控制台显示中文内容供调试
```

#### 1.3 log_input_end 方法
**修改前：**
```python
def log_input_end(self, response: str = ""):
    self.send_message("INPUT_END")
    if response:
        response_clean = response.replace('\n', ' ').replace('\r', ' ')
        self.send_message(f"PARTICIPANT_RESPONSE {response_clean}")
```

**修改后：**
```python
def log_input_end(self, response: str = ""):
    self.send_message("INPUT_END")
    if response:
        self.send_message(f"PARTICIPANT_RESPONSE answer_{self.current_trial}")
        print(f"参与者回答: {response[:50]}...")  # 在控制台显示中文内容供调试
```

#### 1.4 log_answer_display 方法
**修改前：**
```python
def log_answer_display(self, answer: str):
    self.send_message("ANSWER_DISPLAY_START")
    answer_clean = answer.replace('\n', ' ').replace('\r', ' ')
    self.send_message(f"ANSWER_CONTENT {answer_clean}")
```

**修改后：**
```python
def log_answer_display(self, answer: str, question_id: int = None):
    self.send_message("ANSWER_DISPLAY_START")
    if question_id is not None:
        self.send_message(f"ANSWER_CONTENT answer_{question_id}")
    else:
        self.send_message(f"ANSWER_CONTENT answer_{self.current_trial}")
    print(f"显示答案: {answer[:50]}...")  # 在控制台显示中文内容供调试
```

### 2. experiment_flow.py 修改

#### 2.1 传递题目ID参数
修改了三个调用位置，传递题目ID：

```python
# 试次开始
self.eyelink.log_trial_start(trial_num, question_data['question'], question_data['id'])

# 题目显示
self.eyelink.log_question_display(question_data['question'], question_data['id'])

# 答案显示
self.eyelink.log_answer_display(question_data['answer'], question_data['id'])
```

## 修改效果

### 发送给EyeLink的消息格式（全英文）：
```
EXPERIMENT_START
PARTICIPANT_ID test_participant
TRIAL_START 1
QUESTION_ID question_55
BASELINE_START FIXATION
DISPLAY_FIXATION_CROSS
BASELINE_END FIXATION
QUESTION_DISPLAY_START
QUESTION_CONTENT question_55
QUESTION_DISPLAY_END
INPUT_START
DISPLAY_INPUT_BOX
INPUT_END
PARTICIPANT_RESPONSE answer_1
RATING_START CURIOSITY
DISPLAY_RATING_SCALE CURIOSITY
RATING_END CURIOSITY
RATING_VALUE CURIOSITY 4
ANSWER_DISPLAY_START
ANSWER_CONTENT answer_55
ANSWER_DISPLAY_END
TRIAL_END 1
EXPERIMENT_END
```

### 控制台显示（包含中文，供调试）：
```
题目内容: 世界上人口密度最高的国家是哪个?...
显示题目: 世界上人口密度最高的国家是哪个?...
参与者回答: 摩纳哥...
显示答案: 摩纳哥 - 摩纳哥是世界上人口密度最高的国家......
```

## 验证测试

创建了 `test_eyelink_messages.py` 测试脚本，验证：
1. 所有发送给EyeLink的消息都不包含中文字符
2. 消息格式符合预期（使用question_x和answer_x格式）
3. 中文内容仍然在控制台显示，便于调试

测试结果：✅ 所有测试通过！EyeLink消息已正确配置为英文格式

## 使用说明

1. **EyeLink消息**：完全英文，使用题目序号标识
2. **控制台输出**：保留中文显示，便于实验者监控和调试
3. **数据分析**：可以通过题目ID在题库中查找对应的中文内容
4. **向后兼容**：如果不传递question_id参数，会使用trial_num作为默认ID

## 注意事项

1. 确保题库中的题目ID是唯一的
2. 在数据分析时，需要通过ID映射回原始题目内容
3. 所有EyeLink相关的print语句都是在控制台显示，不会发送到EyeLink设备
