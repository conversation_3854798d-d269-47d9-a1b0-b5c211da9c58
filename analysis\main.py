#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主程序 - 运行整个分析流程
"""

import os
import logging
import argparse
from datetime import datetime
from edf_converter import convert_all_edf_files
from data_processor import DataProcessor
from pupil_analyzer import PupilAnalyzer
from visualizer import PupilVisualizer
from config import OUTPUT_DIR, FIGURES_DIR

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(OUTPUT_DIR, 'main.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('main')

def run_analysis(convert_edf=True):
    """运行完整分析流程"""
    start_time = datetime.now()
    logger.info(f"开始分析: {start_time}")
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(FIGURES_DIR, exist_ok=True)
    
    # 步骤1: 转换EDF文件
    if convert_edf:
        logger.info("步骤1: 转换EDF文件")
        converted_files = convert_all_edf_files()
        if not converted_files:
            logger.warning("没有找到或转换EDF文件")
    else:
        logger.info("跳过EDF文件转换")
        converted_files = []
    
    # 步骤2: 处理实验数据
    logger.info("步骤2: 处理实验数据")
    processor = DataProcessor()
    participant_dirs = processor.find_experiment_data()
    
    if not participant_dirs:
        logger.error("未找到实验数据")
        return False
    
    processor.load_trial_data(participant_dirs)
    
    if not processor.trial_data:
        logger.error("未加载到试次数据")
        return False
    
    grouped_data = processor.group_trials_by_ratings()
    processor.save_grouped_data(grouped_data)
    processor.create_summary_statistics(grouped_data)
    
    # 步骤3: 分析瞳孔数据
    logger.info("步骤3: 分析瞳孔数据")
    analyzer = PupilAnalyzer()
    
    trial_pupil_results = {}
    
    for participant_dir in participant_dirs:
        # 查找ASC文件
        asc_files = [f for f in os.listdir(participant_dir) if f.endswith('.asc')]
        
        if not asc_files:
            logger.warning(f"在{participant_dir}中未找到ASC文件")
            continue
        
        for asc_file in asc_files:
            asc_path = os.path.join(participant_dir, asc_file)
            
            # 获取该参与者的试次数据
            participant_id = os.path.basename(participant_dir).split('_')[-1]
            participant_trials = [t for t in processor.trial_data if t.get('participant_id') == participant_id]
            
            if not participant_trials:
                logger.warning(f"未找到参与者{participant_id}的试次数据")
                continue
            
            # 分析瞳孔数据
            trial_results = analyzer.analyze_trial_pupil_data(asc_path, participant_trials)
            trial_pupil_results.update(trial_results)
    
    if not trial_pupil_results:
        logger.error("未分析到瞳孔数据")
        return False
    
    # 步骤4: 可视化
    logger.info("步骤4: 可视化数据")
    visualizer = PupilVisualizer()
    created_plots = visualizer.create_all_plots(trial_pupil_results, grouped_data)
    
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(f"分析完成: {end_time}")
    logger.info(f"总耗时: {duration}")
    
    return {
        'converted_files': converted_files,
        'trial_data': processor.trial_data,
        'grouped_data': grouped_data,
        'trial_pupil_results': trial_pupil_results,
        'created_plots': created_plots
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='运行瞳孔数据分析')
    parser.add_argument('--skip-conversion', action='store_true', help='跳过EDF文件转换')
    args = parser.parse_args()
    
    run_analysis(convert_edf=not args.skip_conversion)
