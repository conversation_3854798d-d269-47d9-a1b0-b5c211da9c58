EyeLink眼动仪实验消息类型说明

实验设置与信息
DISPLAY_COORDS: 记录实验程序所使用的屏幕坐标系范围。
PARTICIPANT_ID: 记录当前被试的唯一身份ID。
EXPERIMENT_TYPE: 记录本次实验的具体类型或名称。
SCREEN_RESOLUTION: 记录实验运行时显示器的屏幕分辨率。

实验流程控制
EXPERIMENT_START: 标记整个实验的正式开始。
EXPERIMENT_END: 标记整个实验的结束。
TRIAL_START: 标记一个新试次（trial）的开始。
TRIAL_END: 标记当前试次的结束。
TOTAL_TRIALS: 记录实验结束时总共完成的试次数。
TOTAL_EVENTS: 记录实验结束时总共发送的自定义事件数量。

校准流程
CALIBRATION_START: 标记眼动仪校准流程的开始。
CALIBRATION_END: 标记眼动仪校准流程的结束，并注明成功或失败。

试次内事件
BASELINE_START: 标记基线数据（如注视点或瞳孔大小）采集阶段的开始。
BASELINE_END: 标记基线数据采集阶段的结束。
DISPLAY_FIXATION_CROSS: 标记屏幕上呈现了用于注视的十字。
QUESTION_DISPLAY_START: 标记问题开始在屏幕上显示的时间点。
QUESTION_DISPLAY_END: 标记问题从屏幕上消失的时间点。
QUESTION_TEXT: 记录当前试次所呈现问题的简短文本内容。
QUESTION_CONTENT: 记录当前试次所呈现问题的完整文本内容。
INPUT_START: 标记被试开始输入答案的阶段。
INPUT_END: 标记被试结束输入答案的阶段。
DISPLAY_INPUT_BOX: 标记屏幕上呈现了用于输入答案的文本框。
PARTICIPANT_RESPONSE: 记录被试输入的具体答案内容。
RATING_START: 标记一个评分任务（如好奇心、愉悦度评分）的开始。
RATING_END: 标记一个评分任务的结束。
DISPLAY_RATING_SCALE: 标记屏幕上呈现了用于评分的量表。
RATING_VALUE: 记录被试在特定评分任务中给出的具体分值。
ANSWER_DISPLAY_START: 标记正确答案开始在屏幕上显示的时间点。
ANSWER_DISPLAY_END: 标记正确答案从屏幕上消失的时间点。
ANSWER_CONTENT: 记录所呈现的正确答案的完整文本内容。