#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据处理器 - 处理实验数据和瞳孔数据
"""

import os
import json
import glob
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from config import DATA_DIR, OUTPUT_DIR, RATING_TYPES, TARGET_PARTICIPANT, LATEST_DATA_ONLY

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(OUTPUT_DIR, 'data_processing.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('data_processor')

class DataProcessor:
    def __init__(self):
        self.trial_data = []
        self.pupil_data = {}
        
    def find_experiment_data(self, data_dir=DATA_DIR, target_participant=TARGET_PARTICIPANT, latest_only=LATEST_DATA_ONLY):
        """查找实验数据文件"""
        logger.info(f"查找实验数据: 目标参与者={target_participant}, 仅最新={latest_only}")
        
        # 查找所有包含实验数据的文件夹
        if target_participant:
            participant_dirs = glob.glob(os.path.join(data_dir, f"*_{target_participant}*"))
        else:
            participant_dirs = glob.glob(os.path.join(data_dir, "*"))
        
        participant_dirs = [d for d in participant_dirs if os.path.isdir(d)]
        logger.info(f"找到{len(participant_dirs)}个参与者文件夹")
        
        if latest_only and participant_dirs:
            # 按照文件夹名称排序（通常包含日期时间）
            participant_dirs.sort(reverse=True)
            # 如果指定了参与者，找到该参与者的最新文件夹
            if target_participant:
                target_dirs = [d for d in participant_dirs if target_participant in os.path.basename(d)]
                if target_dirs:
                    participant_dirs = [target_dirs[0]]  # 只保留最新的
                    logger.info(f"选择最新的目标参与者文件夹: {participant_dirs[0]}")
                else:
                    logger.warning(f"未找到目标参与者 {target_participant} 的文件夹")
                    return []
            else:
                # 只保留最新的文件夹
                participant_dirs = [participant_dirs[0]]
                logger.info(f"选择最新的文件夹: {participant_dirs[0]}")
        
        return participant_dirs
    
    def load_trial_data(self, participant_dirs):
        """加载试次数据"""
        logger.info("加载试次数据")
        
        for participant_dir in participant_dirs:
            # 查找trial文件
            trial_files = glob.glob(os.path.join(participant_dir, "trial_*.json"))
            
            # 也查找experiment_summary.json
            summary_file = os.path.join(participant_dir, "experiment_summary.json")
            
            participant_id = os.path.basename(participant_dir).split('_')[-1]
            
            if os.path.exists(summary_file):
                # 从summary文件加载
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summary_data = json.load(f)
                
                for trial in summary_data.get('trials', []):
                    trial['participant_id'] = participant_id
                    trial['data_dir'] = participant_dir
                    self.trial_data.append(trial)
                    
                logger.info(f"从summary文件加载了{len(summary_data.get('trials', []))}个试次")
            
            elif trial_files:
                # 从单独的trial文件加载
                for trial_file in trial_files:
                    with open(trial_file, 'r', encoding='utf-8') as f:
                        trial_data = json.load(f)
                    trial_data['participant_id'] = participant_id
                    trial_data['data_dir'] = participant_dir
                    self.trial_data.append(trial_data)
                
                logger.info(f"从{len(trial_files)}个trial文件加载数据")
        
        logger.info(f"总共加载了{len(self.trial_data)}个试次")
        return self.trial_data
    
    def group_trials_by_ratings(self, rating_types=None):
        """根据评分对试次进行分组"""
        if rating_types is None:
            rating_types = list(RATING_TYPES.keys())
        
        logger.info(f"根据评分分组试次: {rating_types}")
        
        grouped_data = {}
        
        for rating_type in rating_types:
            grouped_data[rating_type] = {}
            
            for trial in self.trial_data:
                if rating_type in trial:
                    rating_value = trial[rating_type]
                    if rating_value not in grouped_data[rating_type]:
                        grouped_data[rating_type][rating_value] = []
                    grouped_data[rating_type][rating_value].append(trial)
        
        # 打印分组统计
        for rating_type, groups in grouped_data.items():
            logger.info(f"{RATING_TYPES.get(rating_type, rating_type)}分组:")
            # for rating_value, trials in groups.items():
            #     logger.info(f"  评分{rating_value}: {len(trials)}个试次")
            for rating_value, trials in sorted(groups.items()):
                logger.info(f"   评分{rating_value}: {len(trials)}个试次")
        
        return grouped_data
    
    def save_grouped_data(self, grouped_data, output_file=None):
        """保存分组数据"""
        if output_file is None:
            output_file = os.path.join(OUTPUT_DIR, 'grouped_trial_data.json')
        
        # 转换为可序列化的格式
        serializable_data = {}
        for rating_type, groups in grouped_data.items():
            serializable_data[rating_type] = {}
            for rating_value, trials in groups.items():
                serializable_data[rating_type][str(rating_value)] = trials
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分组数据已保存到: {output_file}")
        return output_file
    
    def create_summary_statistics(self, grouped_data):
        """创建汇总统计"""
        logger.info("创建汇总统计")
        
        summary_stats = {}
        
        for rating_type, groups in grouped_data.items():
            summary_stats[rating_type] = {
                'total_trials': sum(len(trials) for trials in groups.values()),
                'rating_distribution': {str(k): len(v) for k, v in groups.items()},
                'mean_rating': np.mean([trial[rating_type] for trial in self.trial_data if rating_type in trial]),
                'std_rating': np.std([trial[rating_type] for trial in self.trial_data if rating_type in trial])
            }
        
        # 保存统计结果
        stats_file = os.path.join(OUTPUT_DIR, 'summary_statistics.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(summary_stats, f, ensure_ascii=False, indent=2)
        
        logger.info(f"汇总统计已保存到: {stats_file}")
        return summary_stats
    
    def process_all_data(self):
        """处理所有数据"""
        logger.info("开始处理所有数据")
        
        # 查找数据文件夹
        participant_dirs = self.find_experiment_data()
        if not participant_dirs:
            logger.error("未找到实验数据")
            return None
        
        # 加载试次数据
        self.load_trial_data(participant_dirs)
        if not self.trial_data:
            logger.error("未加载到试次数据")
            return None
        
        # 分组数据
        grouped_data = self.group_trials_by_ratings()
        
        # 保存分组数据
        self.save_grouped_data(grouped_data)
        
        # 创建汇总统计
        summary_stats = self.create_summary_statistics(grouped_data)
        
        logger.info("数据处理完成")
        return {
            'grouped_data': grouped_data,
            'summary_stats': summary_stats,
            'trial_data': self.trial_data
        }

if __name__ == "__main__":
    processor = DataProcessor()
    result = processor.process_all_data()
