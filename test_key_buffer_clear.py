#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试按键缓存清空功能
验证在每个需要接受按键反馈的界面开始前，都会清空之前的按键缓存
"""

import time
from psychopy import visual, event, core
from experiment_display import ExperimentDisplay

def test_key_buffer_clear():
    """测试按键缓存清空功能"""
    print("测试按键缓存清空功能")
    print("=" * 50)
    print("说明：")
    print("1. 在问题显示期间按回车键（应该不会立即响应）")
    print("2. 等待问题显示时间结束后，进入答案输入界面")
    print("3. 验证之前的回车键不会影响答案输入界面")
    print("=" * 50)
    
    # 创建显示管理器（窗口模式，便于测试）
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        print("\n步骤1: 显示问题（5秒固定时间）")
        print("请在问题显示期间多次按回车键...")
        
        # 显示问题，固定时间5秒
        display.show_question(
            "这是一个测试问题，请在显示期间按回车键测试缓存清空功能。",
            duration=5.0  # 固定5秒，不接受提前按键
        )
        
        print("\n步骤2: 进入答案输入界面")
        print("如果按键缓存清空正常，这里应该等待您的新输入...")
        
        # 获取文本输入
        answer = display.get_text_input("请输入您的答案（测试按键缓存是否清空）：")
        print(f"您输入的答案: {answer}")
        
        print("\n步骤3: 显示评分界面")
        print("如果按键缓存清空正常，这里应该等待您的新评分...")
        
        # 获取评分
        rating = display.get_rating(
            "请评分测试效果（1-5分）",
            (1, 5),
            ["很差", "较差", "一般", "较好", "很好"]
        )
        print(f"您的评分: {rating}")
        
        print("\n步骤4: 显示答案（等待回车）")
        print("请在答案显示期间按回车键...")
        
        # 显示答案，等待回车
        display.show_answer(
            "这是测试答案。按键缓存清空功能测试完成。",
            duration=None  # 等待回车键
        )
        
        print("\n✅ 按键缓存清空功能测试完成！")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出现错误: {e}")
    finally:
        display.close()

def test_rapid_transitions():
    """测试快速界面切换时的按键缓存清空"""
    print("\n测试快速界面切换")
    print("=" * 50)
    
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        # 快速连续的界面切换
        for i in range(3):
            print(f"\n第 {i+1} 轮测试:")
            
            # 显示问题（短时间）
            print("显示问题（2秒）...")
            display.show_question(f"第{i+1}个测试问题", duration=2.0)
            
            # 立即进入评分
            print("进入评分界面...")
            rating = display.get_rating(
                f"第{i+1}次评分",
                (1, 3),
                ["低", "中", "高"]
            )
            print(f"评分结果: {rating}")
            
            if rating is None:
                print("用户取消评分")
                break
        
        print("\n✅ 快速界面切换测试完成！")
        
    except Exception as e:
        print(f"\n快速切换测试出现错误: {e}")
    finally:
        display.close()

def test_manual_key_buffer():
    """手动测试按键缓存行为"""
    print("\n手动测试按键缓存")
    print("=" * 50)
    
    # 创建简单的PsychoPy窗口进行测试
    win = visual.Window(size=(800, 600), fullscr=False, color='black')
    
    try:
        # 测试1: 不清空缓存
        print("测试1: 不清空按键缓存")
        text = visual.TextStim(win, text="请按任意键（不清空缓存）", color='white')
        text.draw()
        win.flip()
        time.sleep(2)  # 等待2秒，用户可能按键
        
        # 立即检查按键（不清空缓存）
        keys = event.getKeys()
        print(f"检测到的按键（不清空）: {keys}")
        
        time.sleep(1)
        
        # 测试2: 清空缓存
        print("测试2: 清空按键缓存")
        text.text = "请按任意键（清空缓存）"
        text.draw()
        win.flip()
        time.sleep(2)  # 等待2秒，用户可能按键
        
        # 清空缓存后检查按键
        event.clearEvents()  # 清空缓存
        time.sleep(0.1)  # 短暂等待
        keys = event.getKeys()
        print(f"检测到的按键（清空后）: {keys}")
        
        print("\n说明：")
        print("- 第一次测试可能检测到之前的按键")
        print("- 第二次测试应该检测不到之前的按键（因为清空了缓存）")
        
    finally:
        win.close()

if __name__ == "__main__":
    print("按键缓存清空功能测试")
    print("=" * 60)
    
    try:
        # 主要测试
        test_key_buffer_clear()
        
        # 快速切换测试
        test_rapid_transitions()
        
        # 手动测试
        test_manual_key_buffer()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("如果在问题显示期间按的回车键没有影响后续界面，说明按键缓存清空功能正常工作。")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出现错误: {e}")
