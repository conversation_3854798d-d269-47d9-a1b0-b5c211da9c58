#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
结果查看器 - 显示分析结果的摘要
"""

import os
import json
import pandas as pd
from config import OUTPUT_DIR, FIGURES_DIR, RATING_TYPES

def load_results():
    """加载分析结果"""
    results = {}
    
    # 加载汇总统计
    stats_file = os.path.join(OUTPUT_DIR, 'summary_statistics.json')
    if os.path.exists(stats_file):
        with open(stats_file, 'r', encoding='utf-8') as f:
            results['statistics'] = json.load(f)
    
    # 加载分组数据
    grouped_file = os.path.join(OUTPUT_DIR, 'grouped_trial_data.json')
    if os.path.exists(grouped_file):
        with open(grouped_file, 'r', encoding='utf-8') as f:
            results['grouped_data'] = json.load(f)
    
    return results

def print_statistics(statistics):
    """打印统计结果"""
    print("=" * 60)
    print("数据分析结果摘要")
    print("=" * 60)
    
    for rating_type, stats in statistics.items():
        rating_name = RATING_TYPES.get(rating_type, rating_type)
        print(f"\n【{rating_name}】")
        print(f"总试次数: {stats['total_trials']}")
        print(f"平均评分: {stats['mean_rating']:.2f}")
        print(f"标准差: {stats['std_rating']:.2f}")
        print("评分分布:")
        
        for rating_value, count in sorted(stats['rating_distribution'].items()):
            percentage = (count / stats['total_trials']) * 100
            print(f"  评分{rating_value}: {count}个试次 ({percentage:.1f}%)")

def print_trial_details(grouped_data):
    """打印试次详情"""
    print("\n" + "=" * 60)
    print("试次详情")
    print("=" * 60)
    
    for rating_type, groups in grouped_data.items():
        rating_name = RATING_TYPES.get(rating_type, rating_type)
        print(f"\n【{rating_name}分组详情】")
        
        for rating_value, trials in groups.items():
            print(f"\n评分{rating_value} ({len(trials)}个试次):")
            for trial in trials:
                trial_num = trial['trial_num']
                question = trial['question'][:30] + "..." if len(trial['question']) > 30 else trial['question']
                print(f"  试次{trial_num}: {question}")

def list_generated_files():
    """列出生成的文件"""
    print("\n" + "=" * 60)
    print("生成的文件")
    print("=" * 60)
    
    print("\n数据文件:")
    if os.path.exists(OUTPUT_DIR):
        for file in os.listdir(OUTPUT_DIR):
            if file.endswith('.json'):
                file_path = os.path.join(OUTPUT_DIR, file)
                size = os.path.getsize(file_path)
                print(f"  {file} ({size} bytes)")
    
    print("\n图形文件:")
    if os.path.exists(FIGURES_DIR):
        for file in os.listdir(FIGURES_DIR):
            if file.endswith('.png'):
                file_path = os.path.join(FIGURES_DIR, file)
                size = os.path.getsize(file_path)
                print(f"  {file} ({size} bytes)")
    
    print("\n日志文件:")
    if os.path.exists(OUTPUT_DIR):
        for file in os.listdir(OUTPUT_DIR):
            if file.endswith('.log'):
                file_path = os.path.join(OUTPUT_DIR, file)
                size = os.path.getsize(file_path)
                print(f"  {file} ({size} bytes)")

def main():
    """主函数"""
    print("EyeLink数据分析结果查看器")
    
    # 加载结果
    results = load_results()
    
    if not results:
        print("未找到分析结果文件，请先运行 python main.py")
        return
    
    # 显示统计结果
    if 'statistics' in results:
        print_statistics(results['statistics'])
    
    # 显示试次详情
    if 'grouped_data' in results:
        print_trial_details(results['grouped_data'])
    
    # 列出生成的文件
    list_generated_files()
    
    print("\n" + "=" * 60)
    print("查看完成")
    print("=" * 60)
    print(f"图形文件位置: {FIGURES_DIR}")
    print(f"数据文件位置: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
