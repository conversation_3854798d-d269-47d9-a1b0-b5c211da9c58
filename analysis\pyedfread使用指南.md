# pyedfread使用指南

## 概述

pyedfread是一个用于解析SR Research EyeLink EDF数据文件的Python库，可以直接将EDF文件转换为pandas DataFrame，避免了使用edf2asc转换的步骤。

## 安装

```bash
pip install git+https://github.com/s-ccs/pyedfread
```

**注意**: 需要安装EyeLink Developers Kit才能正常使用。

## 基本使用方法

### 1. 导入库

```python
import pyedfread
import pandas as pd
import numpy as np
```

### 2. 读取EDF文件

```python
# 读取EDF文件，返回三个DataFrame
samples, events, messages = pyedfread.read_edf('your_file.edf')
```

返回的三个DataFrame包含：
- **samples**: 样本数据（瞳孔大小、注视点坐标等）
- **events**: 事件数据（注视、眼跳、眨眼等）
- **messages**: 消息数据（实验标记、试次信息等）

### 3. 样本数据结构

samples DataFrame的主要列：
- `time`: 时间戳
- `pa_left`: 左眼瞳孔面积/直径
- `pa_right`: 右眼瞳孔面积/直径
- `gx_left`, `gy_left`: 左眼注视点坐标
- `gx_right`, `gy_right`: 右眼注视点坐标
- `flags`: 数据质量标志

### 4. 消息数据解析

```python
# 查看消息内容
print(messages.head())

# 查找特定消息
trial_messages = messages[messages['message'].str.contains('TRIAL_START')]
```

### 5. 瞳孔数据提取

```python
# 提取指定时间段的瞳孔数据
start_time = 1000  # 开始时间戳
end_time = 5000    # 结束时间戳

segment_data = samples[
    (samples['time'] >= start_time) & 
    (samples['time'] <= end_time)
]

# 计算双眼平均瞳孔大小
segment_data['pupil_avg'] = segment_data[['pa_left', 'pa_right']].mean(axis=1)
```

## 在本项目中的集成

### 1. 新增的分析器

创建了`pyedfread_analyzer.py`，包含`PyEDFReadAnalyzer`类：

```python
from pyedfread_analyzer import PyEDFReadAnalyzer

analyzer = PyEDFReadAnalyzer()
success = analyzer.load_edf_data('path/to/file.edf')

if success:
    # 解析消息
    parsed_messages = analyzer.parse_messages()
    
    # 提取瞳孔数据
    pupil_data = analyzer.extract_pupil_data(start_time, end_time)
```

### 2. main.py中的集成

修改了`main.py`的步骤3，现在会：
1. 优先查找EDF文件，使用pyedfread分析
2. 如果没有EDF文件，回退到ASC文件，使用原来的分析器

### 3. 配置参数

在`config.py`中添加了pyedfread相关配置：

```python
PUPIL_CONFIG = {
    # ... 其他配置
    'min_pupil_size': 100.0,      # 最小瞳孔直径
    'max_pupil_size': 8000.0,     # 最大瞳孔直径
    'use_simulated_data': False,  # 是否使用模拟数据
}
```

## 优势

### 相比edf2asc的优势：

1. **直接读取**: 无需转换步骤，直接从EDF文件读取
2. **数据完整性**: 保留原始精度，无转换损失
3. **处理速度**: 避免了文件转换的时间开销
4. **内存效率**: 直接加载到pandas DataFrame
5. **类型安全**: 自动处理数据类型转换

### 相比ASC文件的优势：

1. **精度更高**: EDF文件保留原始数据精度
2. **信息更全**: 包含更多元数据和标志位
3. **处理更快**: 二进制格式读取更快
4. **占用更小**: EDF文件通常比ASC文件小

## 测试和示例

### 1. 功能测试

运行测试脚本：
```bash
python test_pyedfread.py
```

### 2. 使用示例

查看完整示例：
```bash
python pyedfread_example.py
```

### 3. 绘制瞳孔曲线

参考`draw_pupil_line.py`中的实现，使用pyedfread读取数据。

## 注意事项

1. **依赖要求**: 需要安装EyeLink Developers Kit
2. **Windows支持**: Windows系统可能需要额外配置
3. **内存使用**: 大文件可能占用较多内存
4. **错误处理**: 建议添加适当的异常处理

## 故障排除

### 常见问题：

1. **ImportError**: 确保正确安装pyedfread和EyeLink SDK
2. **读取失败**: 检查EDF文件是否损坏
3. **数据为空**: 检查时间范围和事件标记是否正确
4. **编码问题**: 消息中的中文字符可能需要特殊处理

### 解决方案：

1. 重新安装依赖库
2. 验证EDF文件完整性
3. 检查实验程序的消息发送
4. 使用ASCII消息标记

## 总结

pyedfread提供了一种更直接、高效的方式来处理EyeLink数据。通过集成到现有的分析流程中，可以提高数据处理的效率和准确性。建议在有EDF文件的情况下优先使用pyedfread，在没有EDF文件时回退到ASC文件处理。
