#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
详细测试按键缓存清空功能
模拟实际实验流程中的按键缓存问题
"""

import time
from psychopy import visual, event, core
from experiment_display import ExperimentDisplay

def simulate_experiment_flow():
    """模拟实验流程，测试按键缓存清空"""
    print("模拟实验流程测试")
    print("=" * 50)
    print("测试场景：")
    print("1. 显示问题（固定时间，用户可能提前按回车）")
    print("2. 立即进入答案输入（应该不受之前按键影响）")
    print("3. 进入评分（应该不受之前按键影响）")
    print("4. 显示答案（等待回车，应该不受之前按键影响）")
    print("=" * 50)
    
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        # 模拟试次1
        print("\n=== 试次 1 ===")
        
        # 1. 显示问题（固定3秒）
        print("步骤1: 显示问题（3秒固定时间）")
        print("💡 请在问题显示期间按回车键多次...")
        
        display.show_question(
            "世界上最高的山峰是什么？",
            duration=3.0  # 固定3秒
        )
        
        # 2. 答案输入
        print("\n步骤2: 答案输入")
        print("💡 如果按键缓存清空正常，这里应该等待您的新输入...")
        
        answer = display.get_text_input("请输入您的答案：")
        print(f"您的答案: {answer}")
        
        # 3. 好奇心评分
        print("\n步骤3: 好奇心评分")
        print("💡 如果按键缓存清空正常，这里应该等待您的新评分...")
        
        curiosity_rating = display.get_rating(
            "您对这个问题的好奇程度？",
            (1, 5),
            ["不好奇", "稍微好奇", "一般", "比较好奇", "非常好奇"]
        )
        print(f"好奇心评分: {curiosity_rating}")
        
        # 4. 显示答案（等待回车）
        print("\n步骤4: 显示答案")
        print("💡 请在答案显示时按回车键...")
        
        display.show_answer(
            "珠穆朗玛峰 - 海拔8848.86米，是世界上最高的山峰。",
            duration=None  # 等待回车
        )
        
        # 5. 愉悦度评分
        print("\n步骤5: 愉悦度评分")
        print("💡 如果按键缓存清空正常，这里应该等待您的新评分...")
        
        pleasure_rating = display.get_rating(
            "这个答案让您觉得多愉悦？",
            (1, 5),
            ["不愉悦", "稍微愉悦", "一般", "比较愉悦", "非常愉悦"]
        )
        print(f"愉悦度评分: {pleasure_rating}")
        
        print("\n✅ 试次1完成！")
        
        # 模拟试次2（快速测试）
        print("\n=== 试次 2（快速测试）===")
        
        display.show_question("快速测试问题", duration=1.0)
        quick_answer = display.get_text_input("快速答案：")
        quick_rating = display.get_rating("快速评分", (1, 3), ["低", "中", "高"])
        
        print(f"快速测试结果: 答案={quick_answer}, 评分={quick_rating}")
        
        print("\n🎉 实验流程模拟完成！")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出现错误: {e}")
    finally:
        display.close()

def test_key_accumulation():
    """测试按键累积问题"""
    print("\n测试按键累积问题")
    print("=" * 50)
    
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        # 连续快速界面切换
        for i in range(3):
            print(f"\n--- 第 {i+1} 轮快速切换 ---")
            
            # 短时间显示
            print("显示问题（1秒）- 请快速按键...")
            display.show_question(f"快速问题 {i+1}", duration=1.0)
            
            # 立即评分
            print("立即评分 - 检查是否受之前按键影响...")
            rating = display.get_rating(
                f"评分 {i+1}",
                (1, 3),
                ["A", "B", "C"]
            )
            
            if rating is None:
                print("用户取消，测试结束")
                break
            
            print(f"第{i+1}轮评分: {rating}")
        
        print("\n✅ 按键累积测试完成！")
        
    except Exception as e:
        print(f"\n按键累积测试出现错误: {e}")
    finally:
        display.close()

def test_specific_scenarios():
    """测试特定场景"""
    print("\n测试特定场景")
    print("=" * 50)
    
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        # 场景1: 问题显示期间按回车，然后立即输入答案
        print("场景1: 问题显示期间按回车 -> 答案输入")
        print("请在问题显示期间按回车键...")
        
        display.show_question("测试问题：按键缓存清空", duration=2.0)
        
        print("现在进入答案输入，检查是否立即跳过...")
        answer = display.get_text_input("输入答案（检查是否立即跳过）：")
        
        if answer:
            print(f"✅ 正常获得答案: {answer}")
        else:
            print("❌ 答案输入被跳过或取消")
        
        # 场景2: 评分期间按数字，然后立即下一个评分
        print("\n场景2: 评分期间按数字 -> 下一个评分")
        print("请在评分时选择一个数字...")
        
        rating1 = display.get_rating("第一个评分", (1, 3), ["低", "中", "高"])
        print(f"第一个评分: {rating1}")
        
        print("立即进入第二个评分...")
        rating2 = display.get_rating("第二个评分", (1, 3), ["A", "B", "C"])
        print(f"第二个评分: {rating2}")
        
        print("\n✅ 特定场景测试完成！")
        
    except Exception as e:
        print(f"\n特定场景测试出现错误: {e}")
    finally:
        display.close()

if __name__ == "__main__":
    print("详细按键缓存清空功能测试")
    print("=" * 60)
    
    try:
        # 主要实验流程测试
        simulate_experiment_flow()
        
        # 按键累积测试
        test_key_accumulation()
        
        # 特定场景测试
        test_specific_scenarios()
        
        print("\n" + "=" * 60)
        print("🎉 所有详细测试完成！")
        print("\n测试总结：")
        print("✅ 如果在固定时间显示期间的按键没有影响后续界面，说明按键缓存清空正常")
        print("✅ 如果快速界面切换时每个界面都能正常等待输入，说明功能正常")
        print("✅ 如果特定场景下没有出现意外跳过，说明修改成功")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出现错误: {e}")
