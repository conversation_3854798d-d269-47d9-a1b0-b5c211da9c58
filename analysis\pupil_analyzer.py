#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
瞳孔数据分析器 - 分析ASC文件中的瞳孔数据
"""

import os
import re
import pandas as pd
import numpy as np
import logging
from scipy import signal
from scipy.interpolate import interp1d
from config import OUTPUT_DIR, PUPIL_CONFIG, DATA_QUALITY_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(OUTPUT_DIR, 'pupil_analysis.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('pupil_analyzer')

class PupilAnalyzer:
    def __init__(self):
        self.sample_data = None
        self.event_data = None
        self.trial_pupil_data = {}
        
    def parse_asc_file(self, asc_file):
        """解析ASC文件"""
        logger.info(f"解析ASC文件: {asc_file}")

        samples = []
        events = []

        # 尝试不同的编码
        encodings = ['utf-8', 'latin-1', 'cp1252', 'ascii']
        file_content = None

        for encoding in encodings:
            try:
                with open(asc_file, 'r', encoding=encoding) as f:
                    file_content = f.readlines()
                logger.info(f"成功使用{encoding}编码读取文件")
                break
            except UnicodeDecodeError:
                continue

        if file_content is None:
            logger.error(f"无法读取ASC文件: {asc_file}")
            return None, None

        for line in file_content:
            line = line.strip()

            # 解析样本数据
            if line and line[0].isdigit():
                parts = line.split()
                if len(parts) >= 7:  # 时间戳 + 左眼x,y,瞳孔 + 右眼x,y,瞳孔
                    try:
                        timestamp = int(parts[0])
                        left_x = float(parts[1]) if parts[1] != '.' else np.nan
                        left_y = float(parts[2]) if parts[2] != '.' else np.nan
                        left_pupil = float(parts[3]) if parts[3] != '.' else np.nan
                        right_x = float(parts[4]) if parts[4] != '.' else np.nan
                        right_y = float(parts[5]) if parts[5] != '.' else np.nan
                        right_pupil = float(parts[6]) if parts[6] != '.' else np.nan

                        samples.append({
                            'timestamp': timestamp,
                            'left_x': left_x,
                            'left_y': left_y,
                            'left_pupil': left_pupil,
                            'right_x': right_x,
                            'right_y': right_y,
                            'right_pupil': right_pupil
                        })
                    except ValueError:
                        continue

            # 解析事件数据
            elif line.startswith('MSG'):
                parts = line.split('\t', 2)
                if len(parts) >= 3:
                    timestamp = int(parts[1])
                    message = parts[2]
                    events.append({
                        'timestamp': timestamp,
                        'message': message
                    })
        
        self.sample_data = pd.DataFrame(samples)
        self.event_data = pd.DataFrame(events)
        
        logger.info(f"解析完成: {len(samples)}个样本, {len(events)}个事件")
        return self.sample_data, self.event_data
    
    def find_trial_events(self, trial_num):
        """查找特定试次的事件"""
        if self.event_data is None or self.event_data.empty or 'message' not in self.event_data.columns:
            logger.warning(f"没有找到事件数据或事件数据格式不正确，使用样本数据估计时间范围")

            # 如果没有事件数据，使用样本数据估计时间范围
            if self.sample_data is not None and not self.sample_data.empty:
                # 估计每个试次的时间范围
                # 假设每个试次大约30秒
                trial_duration = 30000  # 30秒，单位毫秒

                # 获取样本数据的时间范围
                min_time = self.sample_data['timestamp'].min()
                max_time = self.sample_data['timestamp'].max()

                # 估计试次的开始和结束时间
                # 简单地将总时间范围平均分配给所有试次
                total_duration = max_time - min_time
                trial_count = 15  # 假设有15个试次

                # 计算当前试次的估计时间范围
                start_time = min_time + (trial_num - 1) * (total_duration / trial_count)
                end_time = start_time + trial_duration

                logger.info(f"估计试次{trial_num}的时间范围: {start_time} - {end_time}")
                return start_time, end_time

            return None, None

        try:
            # 查找试次开始和结束事件
            trial_events = self.event_data[
                self.event_data['message'].str.contains(f'trial_{trial_num}', case=False, na=False)
            ]

            # 查找问题显示事件
            question_start_events = self.event_data[
                self.event_data['message'].str.contains(PUPIL_CONFIG['event_start'], case=False, na=False)
            ]

            question_end_events = self.event_data[
                self.event_data['message'].str.contains(PUPIL_CONFIG['event_end'], case=False, na=False)
            ]

            # 找到对应试次的事件
            start_time = None
            end_time = None

            if not question_start_events.empty and not question_end_events.empty:
                # 简单匹配：找到时间最接近的事件
                start_time = question_start_events.iloc[0]['timestamp']
                end_time = question_end_events.iloc[0]['timestamp']

            return start_time, end_time
        except Exception as e:
            logger.error(f"查找试次事件时出错: {e}")
            return None, None
    
    def extract_trial_pupil_data(self, trial_num, start_time, end_time):
        """提取试次的瞳孔数据"""
        if self.sample_data is None:
            return None
        
        # 提取时间段内的数据
        trial_samples = self.sample_data[
            (self.sample_data['timestamp'] >= start_time) & 
            (self.sample_data['timestamp'] <= end_time)
        ].copy()
        
        if trial_samples.empty:
            logger.warning(f"试次{trial_num}没有找到瞳孔数据")
            return None
        
        # 计算双眼平均瞳孔大小
        if PUPIL_CONFIG['eyes_to_use'] == 'both':
            trial_samples['pupil_size'] = trial_samples[['left_pupil', 'right_pupil']].mean(axis=1)
        elif PUPIL_CONFIG['eyes_to_use'] == 'left':
            trial_samples['pupil_size'] = trial_samples['left_pupil']
        elif PUPIL_CONFIG['eyes_to_use'] == 'right':
            trial_samples['pupil_size'] = trial_samples['right_pupil']
        
        # 相对时间（从开始时间算起）
        trial_samples['relative_time'] = trial_samples['timestamp'] - start_time
        
        return trial_samples
    
    def clean_pupil_data(self, pupil_data):
        """清理瞳孔数据"""
        if pupil_data is None or pupil_data.empty:
            return None
        
        cleaned_data = pupil_data.copy()
        
        # 过滤异常值
        if DATA_QUALITY_CONFIG['outlier_threshold'] > 0:
            pupil_mean = cleaned_data['pupil_size'].mean()
            pupil_std = cleaned_data['pupil_size'].std()
            threshold = DATA_QUALITY_CONFIG['outlier_threshold'] * pupil_std
            
            outliers = (np.abs(cleaned_data['pupil_size'] - pupil_mean) > threshold)
            cleaned_data.loc[outliers, 'pupil_size'] = np.nan
        
        # 插值缺失数据
        if PUPIL_CONFIG['interpolate_missing']:
            cleaned_data['pupil_size'] = cleaned_data['pupil_size'].interpolate(method='linear')
        
        # 平滑数据
        if PUPIL_CONFIG['smooth_data'] and PUPIL_CONFIG['smooth_window'] > 0:
            window_size = PUPIL_CONFIG['smooth_window']
            if len(cleaned_data) > window_size:
                cleaned_data['pupil_size'] = signal.savgol_filter(
                    cleaned_data['pupil_size'].fillna(method='ffill').fillna(method='bfill'),
                    window_length=min(window_size, len(cleaned_data)//2*2-1),
                    polyorder=2
                )
        
        return cleaned_data
    
    def analyze_trial_pupil_data(self, asc_file, trial_data):
        """分析试次瞳孔数据"""
        logger.info(f"分析瞳孔数据: {asc_file}")
        
        # 解析ASC文件
        self.parse_asc_file(asc_file)
        
        trial_pupil_results = {}
        
        for trial in trial_data:
            trial_num = trial['trial_num']
            
            # 查找试次事件
            start_time, end_time = self.find_trial_events(trial_num)
            
            if start_time is None or end_time is None:
                logger.warning(f"试次{trial_num}未找到有效的开始/结束时间")
                continue
            
            # 提取瞳孔数据
            pupil_data = self.extract_trial_pupil_data(trial_num, start_time, end_time)
            
            if pupil_data is None:
                continue
            
            # 清理数据
            cleaned_data = self.clean_pupil_data(pupil_data)
            
            if cleaned_data is None or cleaned_data.empty:
                continue
            
            # 保存结果
            trial_pupil_results[trial_num] = {
                'trial_info': trial,
                'pupil_data': cleaned_data,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time
            }
        
        logger.info(f"成功分析了{len(trial_pupil_results)}个试次的瞳孔数据")
        return trial_pupil_results

if __name__ == "__main__":
    analyzer = PupilAnalyzer()
    # 测试代码可以在这里添加
