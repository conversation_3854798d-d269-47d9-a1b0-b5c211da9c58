#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试pyedfread功能
"""

import os
import sys
import pandas as pd
import numpy as np
from pyedfread_analyzer import PyEDFReadAnalyzer

def test_pyedfread_installation():
    """测试pyedfread是否正确安装"""
    print("测试pyedfread安装...")
    
    try:
        import pyedfread
        print("✓ pyedfread 已安装")
        return True
    except ImportError as e:
        print(f"❌ pyedfread 未安装: {e}")
        print("请运行: pip install git+https://github.com/s-ccs/pyedfread")
        return False

def test_edf_file_reading():
    """测试EDF文件读取"""
    print("\n测试EDF文件读取...")
    
    # 查找EDF文件
    data_dir = "../data"
    edf_files = []
    
    if os.path.exists(data_dir):
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith('.edf'):
                    edf_files.append(os.path.join(root, file))
    
    if not edf_files:
        print("❌ 未找到EDF文件")
        return False
    
    print(f"找到 {len(edf_files)} 个EDF文件:")
    for edf_file in edf_files:
        print(f"  - {edf_file}")
    
    # 测试读取第一个EDF文件
    test_file = edf_files[0]
    print(f"\n测试读取: {test_file}")
    
    analyzer = PyEDFReadAnalyzer()
    success = analyzer.load_edf_data(test_file)
    
    if success:
        print("✓ EDF文件读取成功")
        
        # 显示数据信息
        if analyzer.samples is not None:
            print(f"  样本数据: {len(analyzer.samples)} 行")
            print(f"  样本数据列: {list(analyzer.samples.columns)}")
            
            # 检查瞳孔数据
            if 'pa_left' in analyzer.samples.columns:
                left_valid = (analyzer.samples['pa_left'] > 0).sum()
                print(f"  左眼有效瞳孔数据: {left_valid} 个")
            
            if 'pa_right' in analyzer.samples.columns:
                right_valid = (analyzer.samples['pa_right'] > 0).sum()
                print(f"  右眼有效瞳孔数据: {right_valid} 个")
        
        if analyzer.events is not None:
            print(f"  事件数据: {len(analyzer.events)} 行")
            if not analyzer.events.empty:
                print(f"  事件数据列: {list(analyzer.events.columns)}")
        
        if analyzer.messages is not None:
            print(f"  消息数据: {len(analyzer.messages)} 行")
            if not analyzer.messages.empty:
                print(f"  消息数据列: {list(analyzer.messages.columns)}")
                
                # 显示前几条消息
                print("  前5条消息:")
                for i, (_, row) in enumerate(analyzer.messages.head().iterrows()):
                    print(f"    {i+1}. {row['time']}: {row['message']}")
        
        return True
    else:
        print("❌ EDF文件读取失败")
        return False

def test_message_parsing():
    """测试消息解析"""
    print("\n测试消息解析...")
    
    # 查找EDF文件
    data_dir = "../data"
    edf_files = []
    
    if os.path.exists(data_dir):
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith('.edf'):
                    edf_files.append(os.path.join(root, file))
    
    if not edf_files:
        print("❌ 未找到EDF文件")
        return False
    
    test_file = edf_files[0]
    analyzer = PyEDFReadAnalyzer()
    
    if analyzer.load_edf_data(test_file):
        parsed_messages = analyzer.parse_messages()
        
        if parsed_messages:
            print(f"✓ 解析到 {len(parsed_messages)} 个试次的消息")
            
            for trial_num, trial_data in parsed_messages.items():
                print(f"  试次 {trial_num}:")
                for event, timestamp in trial_data.items():
                    print(f"    {event}: {timestamp}")
                if trial_num >= 3:  # 只显示前3个试次
                    break
            
            return True
        else:
            print("❌ 未解析到试次消息")
            return False
    else:
        return False

def test_pupil_data_extraction():
    """测试瞳孔数据提取"""
    print("\n测试瞳孔数据提取...")
    
    # 查找EDF文件
    data_dir = "../data"
    edf_files = []
    
    if os.path.exists(data_dir):
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith('.edf'):
                    edf_files.append(os.path.join(root, file))
    
    if not edf_files:
        print("❌ 未找到EDF文件")
        return False
    
    test_file = edf_files[0]
    analyzer = PyEDFReadAnalyzer()
    
    if analyzer.load_edf_data(test_file):
        parsed_messages = analyzer.parse_messages()
        
        if parsed_messages:
            # 测试第一个试次的数据提取
            first_trial = list(parsed_messages.keys())[0]
            trial_data = parsed_messages[first_trial]
            
            if 'QUESTION_DISPLAY_START' in trial_data and 'QUESTION_DISPLAY_END' in trial_data:
                start_time = trial_data['QUESTION_DISPLAY_START']
                end_time = trial_data['QUESTION_DISPLAY_END']
                
                print(f"测试试次 {first_trial} 瞳孔数据提取")
                print(f"  时间范围: {start_time} - {end_time} ({end_time - start_time} ms)")
                
                pupil_data = analyzer.extract_pupil_data(start_time, end_time)
                
                if not pupil_data.empty:
                    print(f"✓ 提取到 {len(pupil_data)} 个瞳孔数据点")
                    print(f"  数据列: {list(pupil_data.columns)}")
                    
                    # 显示数据统计
                    if 'pupil_size' in pupil_data.columns:
                        valid_data = pupil_data['pupil_size'].dropna()
                        if len(valid_data) > 0:
                            print(f"  有效瞳孔数据: {len(valid_data)} 个")
                            print(f"  瞳孔大小范围: {valid_data.min():.2f} - {valid_data.max():.2f}")
                            print(f"  平均瞳孔大小: {valid_data.mean():.2f} ± {valid_data.std():.2f}")
                        else:
                            print("  ❌ 没有有效的瞳孔数据")
                    
                    return True
                else:
                    print("❌ 未提取到瞳孔数据")
                    return False
            else:
                print("❌ 未找到问题显示事件")
                return False
        else:
            print("❌ 未解析到消息")
            return False
    else:
        return False

def main():
    """主测试函数"""
    print("pyedfread功能测试")
    print("=" * 50)
    
    # 测试1: 检查pyedfread安装
    if not test_pyedfread_installation():
        return
    
    # 测试2: 测试EDF文件读取
    if not test_edf_file_reading():
        return
    
    # 测试3: 测试消息解析
    if not test_message_parsing():
        return
    
    # 测试4: 测试瞳孔数据提取
    if not test_pupil_data_extraction():
        return
    
    print("\n" + "=" * 50)
    print("✓ 所有测试通过！pyedfread功能正常")

if __name__ == "__main__":
    main()
