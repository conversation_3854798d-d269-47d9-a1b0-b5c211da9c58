# EyeLink数据分析历史命令记录

## 2025-07-11 执行的命令
python test_eyelink_messages.py  # 测试EyeLink消息发送，确保没有中文字符
python test_key_buffer_clear.py  # 测试按键缓存清空功能
在analysis中，参考draw_pupil_line程序如何用pyedfread库读取瞳孔数据，先上网查如何使用pyedfread把edf文件转换成data frame，然后之后的main.py中的步骤三和其他步骤都用这个库来获取数据，分析数据。
创建了pyedfread_analyzer.py - 基于pyedfread的瞳孔数据分析器
修改了main.py - 在步骤3中集成pyedfread分析器，优先使用EDF文件，回退到ASC文件
更新了config.py - 添加pyedfread相关配置参数
创建了test_pyedfread.py - 测试pyedfread功能的脚本
创建了pyedfread_example.py - pyedfread使用示例和教程
创建了pyedfread使用指南.md - 详细的使用文档和说明
修复了pyedfread_analyzer.py中的编码问题（移除了✓符号）
成功测试了main.py --skip-conversion，pyedfread分析器正常工作
创建了pyedfread集成总结.md - 完整的集成工作总结
在主程序中添加题目屏蔽功能，可以配置屏蔽特定题目ID
python test_key_buffer_detailed.py  # 详细测试按键缓存清空功能
python analysis\draw_pupil_line.py  # 使用pyedfread绘制瞳孔直径曲线

## 2025-07-10 执行的命令
cd analysis; python main.py  # 成功运行完整分析流程，处理gyd的最新数据
cd analysis; python view_results.py  # 查看分析结果摘要

## 创建分析系统
python main.py  # 运行完整分析流程
python main.py --skip-conversion  # 跳过EDF转换，直接分析现有ASC文件

## 单独运行各个模块
python edf_converter.py  # 只转换EDF文件
python data_processor.py  # 只处理实验数据
python pupil_analyzer.py  # 只分析瞳孔数据
python visualizer.py  # 只进行可视化

## 配置修改
# 修改config.py中的参数来调整分析设置

## 输出文件位置
# ./output/ - 数据处理结果
# ./figures/ - 生成的图形
# ./output/*.log - 日志文件
